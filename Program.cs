﻿using System.Net;

public class Program
{
    public static async Task Main(string[] args)
    {
        // Setup graceful shutdown mechanism
        var cts = new CancellationTokenSource();
        Console.CancelKeyPress += (sender, e) =>
        {
            Console.WriteLine("Shutdown signal received...");
            cts.Cancel();
            e.Cancel = true; // Prevent the process from terminating immediately
        };

        // In later stages, we will use this directory.
        string? directory = null;
        if (args.Length > 1 && args[0] == "--directory")
        {
            directory = args[1];
            Console.WriteLine($"Serving files from directory: {directory}");
        }

        Console.WriteLine("Server starting on port 4221...");
        var server = new HttpServer(4221, directory);

        try
        {
            await server.StartAsync(cts.Token);
        }
        catch (OperationCanceledException)
        {
            // This is expected on graceful shutdown.
            Console.WriteLine("Server shutdown gracefully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An unexpected error occurred: {ex.Message}");
        }
    }
}

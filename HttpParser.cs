using System.Net.Sockets;
using System.Text;

public static class HttpParser
{
    public static async Task<HttpRequest?> ParseAsync(NetworkStream stream, CancellationToken token)
    {
        // Use a StreamReader to easily read line-by-line.
        // Leave the stream open so the handler can potentially read the body later.
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);

        // Read the request line (e.g., "GET /index.html HTTP/1.1")
        string? requestLine = await reader.ReadLineAsync(token);
        if (string.IsNullOrEmpty(requestLine))
        {
            return null;
        }

        string[] requestLineParts = requestLine.Split(' ');
        if (requestLineParts.Length != 3)
        {
            return null;
        }

        var request = new HttpRequest
        {
            Method = requestLineParts[0],
            Path = requestLineParts[1],
            Version = requestLineParts[2]
        };

        // Read headers
        string? headerLine;
        while (!string.IsNullOrEmpty(headerLine = await reader.ReadLineAsync(token)))
        {
            var headerParts = headerLine.Split(':', 2);
            if (headerParts.Length == 2)
            {
                request.Headers[headerParts[0].Trim()] = headerParts[1].Trim();
            }
        }

        // Body parsing will be added in a later stage.

        return request;
    }
}

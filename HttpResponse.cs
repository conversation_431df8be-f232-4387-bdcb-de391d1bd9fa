using System.Text;

public class HttpResponse
{
    public int StatusCode { get; set; }
    public string StatusMessage { get; set; } = string.Empty;
    public Dictionary<string, string> Headers { get; set; } = new(StringComparer.OrdinalIgnoreCase)
    {
        // Default header to signal we close the connection after the response.
        { "Connection", "close" }
    };
    public byte[]? Body { get; set; }

    public byte[] ToBytes()
    {
        var sb = new StringBuilder();

        // Status Line
        sb.Append($"HTTP/1.1 {StatusCode} {StatusMessage}\r\n");

        // Headers
        foreach (var header in Headers)
        {
            sb.Append($"{header.Key}: {header.Value}\r\n");
        }

        // End of headers
        sb.Append("\r\n");

        var headerBytes = Encoding.UTF8.GetBytes(sb.ToString());

        // If there is a body, combine headers and body
        if (Body != null && Body.Length > 0)
        {
            var responseBytes = new byte[headerBytes.Length + Body.Length];
            Buffer.BlockCopy(headerBytes, 0, responseBytes, 0, headerBytes.Length);
            Buffer.BlockCopy(Body, 0, responseBytes, headerBytes.Length, Body.Length);
            return responseBytes;
        }

        return headerBytes;
    }
}

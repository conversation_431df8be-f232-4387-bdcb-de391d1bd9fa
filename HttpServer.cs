using System.Net;
using System.Net.Sockets;
using System.Text;

public class HttpServer
{
    private readonly int _port;
    private readonly string? _fileDirectory;
    private readonly TcpListener _listener;

    public HttpServer(int port, string? fileDirectory)
    {
        _port = port;
        _fileDirectory = fileDirectory;
        _listener = new TcpListener(IPAddress.Any, _port);
    }

    public async Task StartAsync(CancellationToken token)
    {
        _listener.Start();
        Console.WriteLine($"Listening for connections on port {_port}");

        while (!token.IsCancellationRequested)
        {
            try
            {
                TcpClient client = await _listener.AcceptTcpClientAsync(token);
                Console.WriteLine("Client connected...");
                // Fire-and-forget handler for concurrency
                _ = HandleClientAsync(client, token);
            }
            catch (OperationCanceledException)
            {
                // This exception is thrown by AcceptTcpClientAsync when the token is cancelled.
                // We can safely ignore it and let the loop terminate.
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accepting client: {ex.Message}");
            }
        }

        _listener.Stop();
    }

    private async Task HandleClientAsync(TcpClient client, CancellationToken token)
    {
        try
        {
            // Using block ensures the client and stream are disposed properly
            using (client)
            await using (var stream = client.GetStream())
            {
                // For now, we don't need to read the request, just send a response.
                // We will add request parsing in the next stage.

                var response = new HttpResponse
                {
                    StatusCode = 200,
                    StatusMessage = "OK"
                };

                byte[] responseBytes = response.ToBytes();
                await stream.WriteAsync(responseBytes, token);
                await stream.FlushAsync(token);
                Console.WriteLine("Sent 200 OK response.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
             Console.WriteLine("Client connection closed.");
        }
    }
}
